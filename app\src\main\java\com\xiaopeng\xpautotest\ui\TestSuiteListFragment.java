package com.xiaopeng.xpautotest.ui;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;
import com.xiaopeng.xpautotest.BuildConfig;
import com.xiaopeng.xpautotest.R;
import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.xpautotest.adapter.TestSuiteAdapter;
import com.xiaopeng.xpautotest.helper.DialogHelper;
import com.xiaopeng.xpautotest.manager.TestManager;
import com.xiaopeng.xpautotest.model.ITestDataState;
import com.xiaopeng.xpautotest.model.TestCaseItem;
import com.xiaopeng.xpautotest.utils.ApiRouterUtils;
import com.xiaopeng.xpautotest.viewmodel.SuiteViewModel;
import com.xiaopeng.xui.widget.XImageView;
import com.xiaopeng.xui.app.XDialogInterface;
import java.util.ArrayList;
import java.util.List;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

public class TestSuiteListFragment extends Fragment {
    private static final String TAG = "TestSuiteListFragment";
    private SuiteViewModel viewModel;
    private TestSuiteAdapter adapter;

    // 缓存失败用例数据，避免重复查询
    private List<TestCaseItem> cachedFailedCases = new ArrayList<>();
    private XImageView btnExecute;
    private XImageView btnDebug;
    private XImageView btnRetryAllFailed;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Log.i("onCreateView(" + getClass().getSimpleName() + "):" + this.hashCode());

        return inflater.inflate(R.layout.at_fragment_tab,container,false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Log.i("TestSuiteListFragment", "TestSuiteListFragment onViewCreated: ");

        btnExecute = view.findViewById(R.id.btn_execute);
        btnExecute.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (BuildConfig.DEBUG) {
                    Log.i(TAG, "onClick: btnExecute");
                }
                if (viewModel.getTestSuites().getValue() == null || viewModel.getTestSuites().getValue().isEmpty()) {
                    Log.e(TAG, "onClick: test suites is null");
                    // 弹出toast提示
                    Toast.makeText(getContext(), getContext().getString(R.string.executor_no_task), Toast.LENGTH_SHORT).show();
                    return;
                }

                // 显示确认弹窗
                showExecuteAllConfirmDialog();
            }
        });

        // 初始化RecyclerView
        RecyclerView rvCollections = view.findViewById(R.id.rv_test_suite_list);
        rvCollections.setLayoutManager(new LinearLayoutManager(requireContext()));
        adapter = new TestSuiteAdapter(new ArrayList<>(), collection ->
                viewModel.selectTestSuite(collection)
        );
        rvCollections.setAdapter(adapter);
        Log.i("TestSuiteListFragment", "adapter item count: ");

        // 观察数据变化
        viewModel.getTestSuites().observe(getViewLifecycleOwner(), suiteItems -> {
            adapter.updateData(suiteItems, viewModel.getCurrentSuiteId(), viewModel.getLoadMode());
            viewModel.resetLoadMode();
            // 更新重跑所有失败用例按钮的可见性
            updateRetryAllFailedButtonVisibility();
        });

        // 观察自动执行事件
        viewModel.getRequestAutoStart().observe(getViewLifecycleOwner(), shouldStart -> {
            if (shouldStart != null && shouldStart) {
                // 只在 shouldStart 为 true 时行动
                if (btnExecute != null) {
                    btnExecute.performClick();
                }
                viewModel.onAutoStartEventConsumed();
            }
        });

        btnDebug = view.findViewById(R.id.btn_debug);
        btnDebug.setVisibility(ApiRouterUtils.isFactoryMode() ? View.GONE : View.VISIBLE);
        btnDebug.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (BuildConfig.DEBUG) {
                    Log.i(TAG, "onClick: btnDebug");
                }
                ((MainActivity) getContext()).startDebugModeService();
            }
        });

        // 初始化重跑所有失败用例按钮
        btnRetryAllFailed = view.findViewById(R.id.btn_retry_all_failed);
        btnRetryAllFailed.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (BuildConfig.DEBUG) {
                    Log.i(TAG, "onClick: btnRetryAllFailed");
                }
                // 显示确认弹窗
                showRetryAllFailedConfirmDialog();
            }
        });
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "onCreate: ");
        }

        // 获取共享ViewModel
        viewModel = new ViewModelProvider(requireActivity()).get(SuiteViewModel.class);
    }

    @Override
    public void onStart() {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "onStart(" + getClass().getSimpleName() + "):" + this.hashCode());
        }
        super.onStart();
    }

    public void onResume() {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "onResume(" + getClass().getSimpleName() + "):" + this.hashCode());
        }
        super.onResume();
//        tryUpdateLan();
    }

    public void onPause() {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "onPause(" + getClass().getSimpleName() + "):" + this.hashCode());
        }
        super.onPause();
    }

    @Override
    public void onStop() {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "onStop(" + getClass().getSimpleName() + "):" + this.hashCode());
        }
        super.onStop();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Log.i(TAG, "onDestroyView: ");
    }

    public void onActivityNewIntent(boolean isUpdate) {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "onActivityNewIntent(" + getClass().getSimpleName() + "):" + this.hashCode());
        }
    }

    /**
     * 更新重跑所有失败用例按钮的可见性
     */
    private void updateRetryAllFailedButtonVisibility() {
        if (btnRetryAllFailed == null) {
            Log.w(TAG, "updateRetryAllFailedButtonVisibility: btnRetryAllFailed is null");
            return;
        }
        // 获取失败用例并缓存，避免重跑时重复查询
        cachedFailedCases = viewModel.getAllFailedCases();
        Log.i(TAG, "updateRetryAllFailedButtonVisibility: all failed cases count = " + cachedFailedCases.size());
        if (!cachedFailedCases.isEmpty()) {
            btnRetryAllFailed.setVisibility(View.VISIBLE);
        } else {
            btnRetryAllFailed.setVisibility(View.GONE);
        }
    }

    /**
     * 重跑所有套件的所有失败用例
     */
    private void retryAllFailedCases() {
        Log.i(TAG, "retryAllFailedCases: called, Found " + cachedFailedCases.size() + " failed cases to retry");
        // 获取当前的executionId，使用失败用例中的taskExecutionId
        long executionId = cachedFailedCases.isEmpty() ? 0L : cachedFailedCases.get(0).getTaskExecutionId();
        // 直接启动失败用例的重跑
        ((MainActivity) getContext()).startFailedCaseTesting(cachedFailedCases, executionId, 0L);
    }

    /**
     * 显示重跑所有失败用例的确认弹窗
     */
    private void showRetryAllFailedConfirmDialog() {
        if (getContext() == null) {
            return;
        }

        String title = getString(R.string.retry_all_failed_confirm_title);
        String message = getString(R.string.retry_all_failed_confirm_message);
        String positiveButtonText = getString(R.string.retry_all_failed_confirm_positive_button);
        String negativeButtonText = getString(R.string.retry_all_failed_confirm_negative_button);

        DialogHelper.getInstance().showDialog(getContext(), title, message,
                positiveButtonText, negativeButtonText, (xDialog, buttonId) -> {
                    xDialog.dismiss();
                    switch (buttonId) {
                        case XDialogInterface.BUTTON_POSITIVE:
                            // 确认，执行重跑
                            retryAllFailedCases();
                            break;

                        case XDialogInterface.BUTTON_NEGATIVE:
                            // 取消，不执行任何操作
                            break;
                    }
                });
    }

    /**
     * 显示执行测试的确认弹窗
     */
    private void showExecuteAllConfirmDialog() {
        if (getContext() == null) {
            return;
        }

        String title = getString(R.string.execute_all_confirm_title);
        String message = getString(R.string.execute_all_confirm_message);
        String positiveButtonText = getString(R.string.execute_all_confirm_positive_button);
        String negativeButtonText = getString(R.string.execute_all_confirm_negative_button);

        DialogHelper.getInstance().showDialog(getContext(), title, message,
                positiveButtonText, negativeButtonText, (xDialog, buttonId) -> {
                    xDialog.dismiss();
                    switch (buttonId) {
                        case XDialogInterface.BUTTON_POSITIVE:
                            // 确认，执行测试
                            executeAllTests();
                            break;

                        case XDialogInterface.BUTTON_NEGATIVE:
                            // 取消，不执行任何操作
                            break;
                    }
                });
    }

    /**
     * 执行所有测试
     */
    private void executeAllTests() {
        viewModel.startTest(new TestManager.ITestDataCallBack<ITestDataState<Long>>() {
            @Override
            public void onUpdate(ITestDataState<Long> state) {
                if (state instanceof ITestDataState.Success) {
                    Long executionId = ((ITestDataState.Success<Long>) state).getData();
                    if (viewModel.getAutoStartSuiteItem() != null) {
                        ((MainActivity) getContext()).startSuiteTesting(viewModel.getAutoStartSuiteItem(), executionId);
                    } else {
                        ((MainActivity) getContext()).startAllTesting(viewModel.getTestSuites().getValue(), executionId);
                    }
                    Log.i(TAG, "start test successful, executionId: " + executionId);
                } else if (state instanceof ITestDataState.Error) {
                    Throwable error = ((ITestDataState.Error<Long>) state).getError();
                    Log.e(TAG, "start all test error: " + error.getMessage());
                }
            }
        });
    }
}
